package uiagentreporterlogic

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/clickPilot"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

const (
	timeoutOfGetTaskStatus  = 30 * time.Second
	intervalOfGetTaskStatus = 5 * time.Second
)

type UpdateUIAgentComponentExecutedStepsLogic struct {
	*BaseLogic
}

func NewUpdateUIAgentComponentExecutedStepsLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateUIAgentComponentExecutedStepsLogic {
	return &UpdateUIAgentComponentExecutedStepsLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// UpdateUIAgentComponentExecutedSteps 更新`UI Agent`组件执行步骤数
func (l *UpdateUIAgentComponentExecutedStepsLogic) UpdateUIAgentComponentExecutedSteps(in *pb.UpdateUIAgentComponentExecutedStepsReq) (
	out *pb.UpdateUIAgentComponentExecutedStepsResp, err error,
) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s",
		common.ConstLockUIAgentComponentRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID, projectID,
	)
	fn := func() error {
		origin, err := model.CheckUIAgentComponentExecutionRecordByExecuteID(
			l.ctx, l.svcCtx.UiAgentComponentExecutionRecordModel, taskID, executeID, projectID,
		)
		if err != nil {
			return err
		}

		timer := timewheel.NewTimer(timeoutOfGetTaskStatus)
		defer timer.Stop()

		ticker := timewheel.NewTicker(intervalOfGetTaskStatus)
		defer ticker.Stop()

		flag := false
		for !flag {
			select {
			case <-l.ctx.Done():
				l.Warnf(
					"got a context done signal while getting the task status, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
					taskID, executeID, projectID, l.ctx.Err(),
				)
				flag = true
			case <-timer.C:
				l.Warnf(
					"timeout while getting the task status, task_id: %s, execute_id: %s, project_id: %s",
					taskID, executeID, projectID,
				)
				flag = true
			case <-ticker.C:
				data, err := l.svcCtx.ClickPilotClient.GetTaskStatus(executeID)
				if err != nil {
					l.Errorf(
						"failed to get task status by ClickPilot, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
						taskID, executeID, projectID, err,
					)
					continue
				}

				switch data.Status {
				case clickPilot.TaskStatusOfSucceed,
					clickPilot.TaskStatusOfFailed,
					clickPilot.TaskStatusOfTerminated,
					clickPilot.TaskStatusOfError:
					flag = true
				}
			}
		}

		steps, err := l.svcCtx.ClickPilotClient.GetTaskRecord(executeID)
		if err != nil {
			l.Errorf(
				"failed to get task record by ClickPilot, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
				taskID, executeID, projectID, err,
			)
			return err
		}

		origin.ExecutedSteps = int64(len(steps))
		return l.svcCtx.UiAgentComponentExecutionRecordModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err = l.svcCtx.UiAgentComponentExecutionRecordModel.Update(context, session, origin); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to modify ui agent component execution record, task_id: %s, execute_id: %s, project_id: %s, component_id: %s, error: %+v",
						taskID, executeID, projectID, in.GetComponentId(), err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockWithOptionDo(
		l.svcCtx.Redis, key, fn,
		redislock.WithTimeout(lockTimeout),
		redislock.WithExpire(timeoutOfGetTaskStatus+intervalOfGetTaskStatus),
	); err != nil {
		return nil, err
	}

	return &pb.UpdateUIAgentComponentExecutedStepsResp{}, nil
}
