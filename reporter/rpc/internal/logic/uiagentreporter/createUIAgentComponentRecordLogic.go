package uiagentreporterlogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreateUIAgentComponentRecordLogic struct {
	*BaseLogic
}

func NewCreateUIAgentComponentRecordLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateUIAgentComponentRecordLogic {
	return &CreateUIAgentComponentRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateUIAgentComponentRecord 创建`UI Agent`组件执行记录
func (l *CreateUIAgentComponentRecordLogic) CreateUIAgentComponentRecord(in *pb.CreateUIAgentComponentRecordReq) (
	out *pb.CreateUIAgentComponentRecordResp, err error,
) {
	var (
		taskID          = in.GetTaskId()
		executeID       = in.GetExecuteId()
		parentExecuteID = in.GetParentExecuteId()
		projectID       = in.GetProjectId()
		status          = in.GetStatus()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s",
		common.ConstLockUIAgentComponentRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID, projectID,
	)
	fn := func() error {
		_, err = model.CheckUIAgentComponentExecutionRecordByExecuteID(
			l.ctx, l.svcCtx.UiAgentComponentExecutionRecordModel, taskID, executeID, projectID,
		)
		if err != nil {
			if re, ok := errorx.RootError(err); ok && re.Code() != errorx.NotExists {
				return err
			}
		} else {
			return errorx.Errorf(
				errorx.AlreadyExists,
				"the ui agent component execution record already exists, task_id: %s, execute_id: %s, project_id: %s",
				taskID, executeID, projectID,
			)
		}

		var times int64 = 1
		if in.GetTimes() > 1 {
			times = int64(in.GetTimes())
		}

		now := time.Now()
		startedAt := now
		if in.GetStartedAt() != nil && in.GetStartedAt().IsValid() && in.GetStartedAt().GetSeconds() > 0 {
			if startTime := in.GetStartedAt().AsTime(); !startTime.IsZero() {
				startedAt = startTime
			}
		}

		executedBy := in.GetExecutedBy()
		if executedBy == "" {
			executedBy = l.currentUser.Account
		}

		createdBy := l.currentUser.Account
		if l.currentUser.Account == systemUser.Account {
			createdBy = executedBy
		}

		record := &model.UiAgentComponentExecutionRecord{
			TaskId:    taskID,
			ExecuteId: executeID,
			ParentExecuteId: sql.NullString{
				String: parentExecuteID,
				Valid:  parentExecuteID != "",
			},
			ProjectId:         projectID,
			ComponentId:       in.GetComponentId(),
			ComponentName:     in.GetComponentName(),
			TriggerMode:       protobuf.GetEnumStringOf(in.GetTriggerMode()),
			ExecuteType:       int64(in.GetExecuteType()),
			ApplicationConfig: protobuf.MarshalJSONToStringIgnoreError(in.GetApplicationConfig()),
			Mode:              int64(in.GetMode()),
			Steps:             protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetSteps()),
			Expectation:       protobuf.MarshalJSONToStringIgnoreError(in.GetExpectation()),
			Variables:         protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetVariables()),
			InputParameters:   protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetInputParameters()),
			OutputParameters:  protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetOutputParameters()),
			ForegroundCheck:   cast.ToInt64(in.GetForegroundCheck()),
			Device:            protobuf.MarshalJSONToStringIgnoreError(in.GetDevice()),
			Reinstall:         cast.ToInt64(in.GetReinstall()),
			Restart:           cast.ToInt64(in.GetRestart()),
			ReferenceId: sql.NullString{
				String: in.GetReferenceId(),
				Valid:  in.GetReferenceId() != "",
			},
			Times:     times,
			Successes: 0,
			Status: sql.NullString{
				String: status,
				Valid:  status != "",
			},
			ExecutedBy: executedBy,
			StartedAt: sql.NullTime{
				Time:  startedAt,
				Valid: true,
			},
			CreatedBy: createdBy,
			UpdatedBy: createdBy,
			CreatedAt: now,
			UpdatedAt: now,
		}

		return l.svcCtx.UiAgentComponentExecutionRecordModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err = l.svcCtx.UiAgentComponentExecutionRecordModel.Insert(context, session, record); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to create ui agent component execution record, task_id: %s, execute_id: %s, project_id: %s, component_id: %s, error: %+v",
						taskID, executeID, projectID, in.GetComponentId(), err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}

	return &pb.CreateUIAgentComponentRecordResp{}, nil
}
