package internal

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/serverinterceptors"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/tasks"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

var noNeedUserMethods = []string{
	pb.Reporter_CreateRecord_FullMethodName,          // "/reporter.reporter/createRecord",
	pb.Reporter_ModifyRecord_FullMethodName,          // "/reporter.reporter/modifyRecord",
	pb.Reporter_GetExecuteRecord_FullMethodName,      // "/reporter.reporter/getExecuteRecord",
	pb.Reporter_GetParentRecord_FullMethodName,       // "/reporter.reporter/getParentRecord",
	pb.Reporter_GetChildrenRecord_FullMethodName,     // "/reporter.reporter/getChildrenRecord",
	pb.Reporter_CreateInterfaceRecord_FullMethodName, // "/reporter.reporter/createInterfaceRecord",
	pb.Reporter_ModifyInterfaceRecord_FullMethodName, // "/reporter.reporter/modifyInterfaceRecord",
	pb.Reporter_GetCaseLatestRecord_FullMethodName,   // "/reporter.reporter/getCaseLatestRecord",
	pb.Reporter_ListInterfaceRecord_FullMethodName,   // "/reporter.reporter/listInterfaceRecord",
	pb.Reporter_GetInterfaceRecord_FullMethodName,    // "/reporter.reporter/getInterfaceRecord",
	pb.Reporter_CreateSuiteRecord_FullMethodName,     // "/reporter.reporter/createSuiteRecord",
	pb.Reporter_ModifySuiteRecord_FullMethodName,     // "/reporter.reporter/modifySuiteRecord",
	pb.Reporter_ListSuiteRecord_FullMethodName,       // "/reporter.reporter/listSuiteRecord",
	pb.Reporter_GetSuiteRecord_FullMethodName,        // "/reporter.reporter/getSuiteRecord",
	pb.Reporter_CreatePlanRecord_FullMethodName,      // "/reporter.reporter/createPlanRecord",
	pb.Reporter_ModifyPlanRecord_FullMethodName,      // "/reporter.reporter/modifyPlanRecord",
	pb.Reporter_ListPlanRecord_FullMethodName,        // "/reporter.reporter/listPlanRecord",
	pb.Reporter_GetPlanRecord_FullMethodName,         // "/reporter.reporter/getPlanRecord",
	pb.Reporter_GetPlanTimeScale_FullMethodName,      // "/reporter.reporter/getPlanTimeScale",
	pb.UIReporter_CreateUIPlanRecord_FullMethodName,  // "/reporter.UIReporter/CreateUIPlanRecord",
	pb.UIReporter_ModifyUIPlanRecord_FullMethodName,  // "/reporter.UIReporter/ModifyUIPlanRecord",
	pb.UIReporter_ListUIPlanRecord_FullMethodName,    // "/reporter.UIReporter/ListUIPlanRecord",
}

func HandleSetupOperations(svcCtx *svc.ServiceContext) error {
	// register no need user full methods for user info server interceptor
	registerNoNeedUserFullMethod()

	if err := registerTasksAndLaunchConsumer(svcCtx); err != nil {
		return errors.Wrap(err, "failed to register tasks or launch consumer")
	}

	return nil
}

func registerNoNeedUserFullMethod() {
	for _, fullMethod := range noNeedUserMethods {
		serverinterceptors.NoNeedUserForFullMethod(fullMethod)
	}
}

func registerTasksAndLaunchConsumer(svcCtx *svc.ServiceContext) error {
	if err := svcCtx.Consumer.RegisterHandlers(
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeReporterHandleUIAgentRecordTask,
			tasks.NewHandleUIAgentRecordTaskProcessor(svcCtx),
		),
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeReporterUpdateUIAgentComponentExecutedStepsTask,
			tasks.NewUpdateUIAgentExecutedStepsTaskProcessor(svcCtx),
		),
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeReporterDeleteRecordTask,
			tasks.NewDeleteRecordTaskProcessor(svcCtx),
		),
	); err != nil {
		logx.Errorf("failed to register tasks to the reporter consumer, error: %+v", err)
		return err
	}

	threading.GoSafe(
		func() {
			logx.Info("starting the reporter consumer")
			svcCtx.Consumer.Start()
		},
	)

	return nil
}
