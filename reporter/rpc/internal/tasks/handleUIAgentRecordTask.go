package tasks

import (
	"context"
	"runtime/debug"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	uiagentreporterlogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/logic/uiagentreporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type HandleUIAgentRecordTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewHandleUIAgentRecordTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &HandleUIAgentRecordTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *HandleUIAgentRecordTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) ([]byte, error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	var (
		info pb.HandleUIAgentRecordTaskInfo
		err  error
	)

	if err = protobuf.UnmarshalJSON(task.Payload, &info); err != nil {
		logger.Errorf(
			"failed to unmarshal the payload of handle ui agent record task, payload: %s, error: %+v",
			task.Payload, err,
		)
		return []byte(constants.FAILURE), nil
	}

	switch v := info.GetPayload().(type) {
	case *pb.HandleUIAgentRecordTaskInfo_CreateComponent:
		_, err = uiagentreporterlogic.NewCreateUIAgentComponentRecordLogic(
			ctx, p.svcCtx,
		).CreateUIAgentComponentRecord(v.CreateComponent)
	case *pb.HandleUIAgentRecordTaskInfo_ModifyComponent:
		_, err = uiagentreporterlogic.NewModifyUIAgentComponentRecordLogic(
			ctx, p.svcCtx,
		).ModifyUIAgentComponentRecord(v.ModifyComponent)
	default:
		logger.Errorf("invalid payload type: %T", v)
		return []byte(constants.FAILURE), nil
	}
	if err != nil {
		logger.Errorf("failed to handle ui agent record, payload: %s, error: %+v", task.Payload, err)
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}
