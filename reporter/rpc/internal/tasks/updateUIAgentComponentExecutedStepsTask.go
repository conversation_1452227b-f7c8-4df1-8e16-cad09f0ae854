package tasks

import (
	"context"
	"runtime/debug"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	uiagentreporterlogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/logic/uiagentreporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UpdateUIAgentComponentExecutedStepsTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewUpdateUIAgentExecutedStepsTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &UpdateUIAgentComponentExecutedStepsTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *UpdateUIAgentComponentExecutedStepsTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) (
	[]byte, error,
) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	var (
		info pb.UpdateUIAgentExecutedStepsTaskInfo
		err  error
	)

	if err = protobuf.UnmarshalJSON(task.Payload, &info); err != nil {
		logger.Errorf(
			"failed to unmarshal the payload of update ui agent component executed steps task, payload: %s, error: %+v",
			task.Payload, err,
		)
		return []byte(constants.FAILURE), nil
	}

	if _, err = uiagentreporterlogic.NewUpdateUIAgentComponentExecutedStepsLogic(
		ctx, p.svcCtx,
	).UpdateUIAgentComponentExecutedSteps(info.GetUpdateComponent()); err != nil {
		logger.Errorf("failed to update ui agent component executed steps, payload: %s, error: %+v", task.Payload, err)
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}
