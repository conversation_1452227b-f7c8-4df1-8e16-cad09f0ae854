// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: reporter/uireporter.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UIPlanRecord struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TaskId               string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                                  // 任务ID
	ExecuteId            string                 `protobuf:"bytes,2,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                                         // UI计划执行ID
	ProjectId            string                 `protobuf:"bytes,11,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                        // 项目ID
	PlanId               string                 `protobuf:"bytes,12,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                 // 计划ID
	PlanName             string                 `protobuf:"bytes,13,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                                           // 计划名称
	Type                 pb.TriggerMode         `protobuf:"varint,14,opt,name=type,proto3,enum=common.TriggerMode" json:"type,omitempty"`                                          // 计划类型（手动、定时、接口）
	PriorityType         pb.PriorityType        `protobuf:"varint,15,opt,name=priority_type,json=priorityType,proto3,enum=common.PriorityType" json:"priority_type,omitempty"`     // 优先级
	DeviceType           pb.DeviceType          `protobuf:"varint,16,opt,name=device_type,json=deviceType,proto3,enum=common.DeviceType" json:"device_type,omitempty"`             // 设备类型（真机、云手机）
	PlatformType         pb.PlatformType        `protobuf:"varint,17,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"`     // 平台类型（Android、iOS）
	GitConfig            *pb.GitConfig          `protobuf:"bytes,18,opt,name=git_config,json=gitConfig,proto3" json:"git_config,omitempty"`                                        // Git配置
	PackageName          string                 `protobuf:"bytes,19,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                                  // 包名
	CallbackUrl          string                 `protobuf:"bytes,20,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url,omitempty"`                                  // 回调地址
	AppDownloadLink      string                 `protobuf:"bytes,21,opt,name=app_download_link,json=appDownloadLink,proto3" json:"app_download_link,omitempty"`                    // APP下载地址
	AppVersion           string                 `protobuf:"bytes,22,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`                                     // APP版本
	AppName              string                 `protobuf:"bytes,23,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                                              // 应用名称
	TestLanguage         pb.TestLanguage        `protobuf:"varint,24,opt,name=test_language,json=testLanguage,proto3,enum=common.TestLanguage" json:"test_language,omitempty"`     // 测试语言
	TestLanguageVersion  string                 `protobuf:"bytes,25,opt,name=test_language_version,json=testLanguageVersion,proto3" json:"test_language_version,omitempty"`        // 测试语言版本
	TestFramework        pb.TestFramework       `protobuf:"varint,26,opt,name=test_framework,json=testFramework,proto3,enum=common.TestFramework" json:"test_framework,omitempty"` // 测试框架
	TestArgs             []string               `protobuf:"bytes,27,rep,name=test_args,json=testArgs,proto3" json:"test_args,omitempty"`                                           // 附加参数
	ExecutionEnvironment string                 `protobuf:"bytes,28,opt,name=execution_environment,json=executionEnvironment,proto3" json:"execution_environment,omitempty"`       // 执行环境
	Devices              []string               `protobuf:"bytes,29,rep,name=devices,proto3" json:"devices,omitempty"`                                                             // 设备列表
	Together             bool                   `protobuf:"varint,30,opt,name=together,proto3" json:"together,omitempty"`                                                          // 选择的设备是否一起执行
	Status               string                 `protobuf:"bytes,41,opt,name=status,proto3" json:"status,omitempty"`                                                               // 执行状态（结果）
	TotalSuite           int64                  `protobuf:"varint,42,opt,name=total_suite,json=totalSuite,proto3" json:"total_suite,omitempty"`                                    // 总的集合数
	SuccessSuite         int64                  `protobuf:"varint,43,opt,name=success_suite,json=successSuite,proto3" json:"success_suite,omitempty"`                              // 执行成功的集合数
	FailureSuite         int64                  `protobuf:"varint,44,opt,name=failure_suite,json=failureSuite,proto3" json:"failure_suite,omitempty"`                              // 执行失败的集合数
	TotalCase            int64                  `protobuf:"varint,45,opt,name=total_case,json=totalCase,proto3" json:"total_case,omitempty"`                                       // 总的用例数
	SuccessCase          int64                  `protobuf:"varint,46,opt,name=success_case,json=successCase,proto3" json:"success_case,omitempty"`                                 // 执行成功的用例数
	FailureCase          int64                  `protobuf:"varint,47,opt,name=failure_case,json=failureCase,proto3" json:"failure_case,omitempty"`                                 // 执行失败的用例数
	CostTime             int64                  `protobuf:"varint,48,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                                          // 耗时
	StartedAt            int64                  `protobuf:"varint,49,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`                                       // 开始时间
	EndedAt              int64                  `protobuf:"varint,50,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`                                             // 结束时间
	ExecutedBy           string                 `protobuf:"bytes,51,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`                                     // 执行者
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *UIPlanRecord) Reset() {
	*x = UIPlanRecord{}
	mi := &file_reporter_uireporter_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIPlanRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIPlanRecord) ProtoMessage() {}

func (x *UIPlanRecord) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_uireporter_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIPlanRecord.ProtoReflect.Descriptor instead.
func (*UIPlanRecord) Descriptor() ([]byte, []int) {
	return file_reporter_uireporter_proto_rawDescGZIP(), []int{0}
}

func (x *UIPlanRecord) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *UIPlanRecord) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *UIPlanRecord) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UIPlanRecord) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *UIPlanRecord) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

func (x *UIPlanRecord) GetType() pb.TriggerMode {
	if x != nil {
		return x.Type
	}
	return pb.TriggerMode(0)
}

func (x *UIPlanRecord) GetPriorityType() pb.PriorityType {
	if x != nil {
		return x.PriorityType
	}
	return pb.PriorityType(0)
}

func (x *UIPlanRecord) GetDeviceType() pb.DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return pb.DeviceType(0)
}

func (x *UIPlanRecord) GetPlatformType() pb.PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return pb.PlatformType(0)
}

func (x *UIPlanRecord) GetGitConfig() *pb.GitConfig {
	if x != nil {
		return x.GitConfig
	}
	return nil
}

func (x *UIPlanRecord) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *UIPlanRecord) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *UIPlanRecord) GetAppDownloadLink() string {
	if x != nil {
		return x.AppDownloadLink
	}
	return ""
}

func (x *UIPlanRecord) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *UIPlanRecord) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *UIPlanRecord) GetTestLanguage() pb.TestLanguage {
	if x != nil {
		return x.TestLanguage
	}
	return pb.TestLanguage(0)
}

func (x *UIPlanRecord) GetTestLanguageVersion() string {
	if x != nil {
		return x.TestLanguageVersion
	}
	return ""
}

func (x *UIPlanRecord) GetTestFramework() pb.TestFramework {
	if x != nil {
		return x.TestFramework
	}
	return pb.TestFramework(0)
}

func (x *UIPlanRecord) GetTestArgs() []string {
	if x != nil {
		return x.TestArgs
	}
	return nil
}

func (x *UIPlanRecord) GetExecutionEnvironment() string {
	if x != nil {
		return x.ExecutionEnvironment
	}
	return ""
}

func (x *UIPlanRecord) GetDevices() []string {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *UIPlanRecord) GetTogether() bool {
	if x != nil {
		return x.Together
	}
	return false
}

func (x *UIPlanRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UIPlanRecord) GetTotalSuite() int64 {
	if x != nil {
		return x.TotalSuite
	}
	return 0
}

func (x *UIPlanRecord) GetSuccessSuite() int64 {
	if x != nil {
		return x.SuccessSuite
	}
	return 0
}

func (x *UIPlanRecord) GetFailureSuite() int64 {
	if x != nil {
		return x.FailureSuite
	}
	return 0
}

func (x *UIPlanRecord) GetTotalCase() int64 {
	if x != nil {
		return x.TotalCase
	}
	return 0
}

func (x *UIPlanRecord) GetSuccessCase() int64 {
	if x != nil {
		return x.SuccessCase
	}
	return 0
}

func (x *UIPlanRecord) GetFailureCase() int64 {
	if x != nil {
		return x.FailureCase
	}
	return 0
}

func (x *UIPlanRecord) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *UIPlanRecord) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *UIPlanRecord) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *UIPlanRecord) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

type UISuiteRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                        // 任务ID
	ExecuteId     string                 `protobuf:"bytes,2,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`               // UI集合执行ID
	PlanExecuteId string                 `protobuf:"bytes,3,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"` // UI计划执行ID
	ProjectId     string                 `protobuf:"bytes,11,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`              // 项目ID
	SuiteId       string                 `protobuf:"bytes,12,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                    // 集合ID
	SuiteName     string                 `protobuf:"bytes,13,opt,name=suite_name,json=suiteName,proto3" json:"suite_name,omitempty"`              // 集合名称
	Udid          string                 `protobuf:"bytes,14,opt,name=udid,proto3" json:"udid,omitempty"`                                         // 设备编号
	Status        string                 `protobuf:"bytes,21,opt,name=status,proto3" json:"status,omitempty"`                                     // 执行状态（结果）
	TotalCase     int64                  `protobuf:"varint,22,opt,name=total_case,json=totalCase,proto3" json:"total_case,omitempty"`             // 总的用例数
	SuccessCase   int64                  `protobuf:"varint,23,opt,name=success_case,json=successCase,proto3" json:"success_case,omitempty"`       // 执行成功的用例数
	FailureCase   int64                  `protobuf:"varint,24,opt,name=failure_case,json=failureCase,proto3" json:"failure_case,omitempty"`       // 执行失败的用例数
	CostTime      int64                  `protobuf:"varint,25,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                // 耗时
	StartedAt     int64                  `protobuf:"varint,26,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`             // 开始时间
	EndedAt       int64                  `protobuf:"varint,27,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`                   // 结束时间
	ExecutedBy    string                 `protobuf:"bytes,28,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`           // 执行者
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UISuiteRecord) Reset() {
	*x = UISuiteRecord{}
	mi := &file_reporter_uireporter_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UISuiteRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UISuiteRecord) ProtoMessage() {}

func (x *UISuiteRecord) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_uireporter_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UISuiteRecord.ProtoReflect.Descriptor instead.
func (*UISuiteRecord) Descriptor() ([]byte, []int) {
	return file_reporter_uireporter_proto_rawDescGZIP(), []int{1}
}

func (x *UISuiteRecord) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *UISuiteRecord) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *UISuiteRecord) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *UISuiteRecord) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UISuiteRecord) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *UISuiteRecord) GetSuiteName() string {
	if x != nil {
		return x.SuiteName
	}
	return ""
}

func (x *UISuiteRecord) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *UISuiteRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UISuiteRecord) GetTotalCase() int64 {
	if x != nil {
		return x.TotalCase
	}
	return 0
}

func (x *UISuiteRecord) GetSuccessCase() int64 {
	if x != nil {
		return x.SuccessCase
	}
	return 0
}

func (x *UISuiteRecord) GetFailureCase() int64 {
	if x != nil {
		return x.FailureCase
	}
	return 0
}

func (x *UISuiteRecord) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *UISuiteRecord) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *UISuiteRecord) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *UISuiteRecord) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

type UICaseRecord struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TaskId         string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                           // 任务ID
	ExecuteId      string                 `protobuf:"bytes,2,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                  // UI用例执行ID
	SuiteExecuteId string                 `protobuf:"bytes,3,opt,name=suite_execute_id,json=suiteExecuteId,proto3" json:"suite_execute_id,omitempty"` // UI集合执行ID
	ProjectId      string                 `protobuf:"bytes,11,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                 // 项目ID
	CaseId         string                 `protobuf:"bytes,12,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                          // 用例ID
	CaseName       string                 `protobuf:"bytes,13,opt,name=case_name,json=caseName,proto3" json:"case_name,omitempty"`                    // 用例名称
	Udid           string                 `protobuf:"bytes,14,opt,name=udid,proto3" json:"udid,omitempty"`                                            // 设备编号
	Status         string                 `protobuf:"bytes,21,opt,name=status,proto3" json:"status,omitempty"`                                        // 执行状态（结果）
	CostTime       int64                  `protobuf:"varint,22,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                   // 耗时
	StartedAt      int64                  `protobuf:"varint,23,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`                // 开始时间
	EndedAt        int64                  `protobuf:"varint,24,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`                      // 结束时间
	ExecutedBy     string                 `protobuf:"bytes,25,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`              // 执行者
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UICaseRecord) Reset() {
	*x = UICaseRecord{}
	mi := &file_reporter_uireporter_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UICaseRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UICaseRecord) ProtoMessage() {}

func (x *UICaseRecord) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_uireporter_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UICaseRecord.ProtoReflect.Descriptor instead.
func (*UICaseRecord) Descriptor() ([]byte, []int) {
	return file_reporter_uireporter_proto_rawDescGZIP(), []int{2}
}

func (x *UICaseRecord) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *UICaseRecord) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *UICaseRecord) GetSuiteExecuteId() string {
	if x != nil {
		return x.SuiteExecuteId
	}
	return ""
}

func (x *UICaseRecord) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UICaseRecord) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *UICaseRecord) GetCaseName() string {
	if x != nil {
		return x.CaseName
	}
	return ""
}

func (x *UICaseRecord) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *UICaseRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UICaseRecord) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *UICaseRecord) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *UICaseRecord) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *UICaseRecord) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

type UICaseStep struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`           // 任务ID
	StepId        string                 `protobuf:"bytes,2,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`           // 步骤ID
	Stage         pb.TestStage           `protobuf:"varint,3,opt,name=stage,proto3,enum=common.TestStage" json:"stage,omitempty"`    // 阶段
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                             // 步骤名称
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`                         // 执行状态（结果）
	Content       string                 `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`                       // 步骤内容
	StartedAt     int64                  `protobuf:"varint,7,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"` // 开始时间
	EndedAt       int64                  `protobuf:"varint,8,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`       // 结束时间
	Index         int64                  `protobuf:"varint,9,opt,name=index,proto3" json:"index,omitempty"`                          // 步骤索引
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UICaseStep) Reset() {
	*x = UICaseStep{}
	mi := &file_reporter_uireporter_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UICaseStep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UICaseStep) ProtoMessage() {}

func (x *UICaseStep) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_uireporter_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UICaseStep.ProtoReflect.Descriptor instead.
func (*UICaseStep) Descriptor() ([]byte, []int) {
	return file_reporter_uireporter_proto_rawDescGZIP(), []int{3}
}

func (x *UICaseStep) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *UICaseStep) GetStepId() string {
	if x != nil {
		return x.StepId
	}
	return ""
}

func (x *UICaseStep) GetStage() pb.TestStage {
	if x != nil {
		return x.Stage
	}
	return pb.TestStage(0)
}

func (x *UICaseStep) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UICaseStep) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UICaseStep) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UICaseStep) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *UICaseStep) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *UICaseStep) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

type UIDeviceRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                        // 任务ID
	PlanExecuteId string                 `protobuf:"bytes,2,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"` // UI计划执行ID
	ProjectId     string                 `protobuf:"bytes,11,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`              // 项目ID
	Udid          string                 `protobuf:"bytes,12,opt,name=udid,proto3" json:"udid,omitempty"`                                         // 设备编号
	Status        string                 `protobuf:"bytes,21,opt,name=status,proto3" json:"status,omitempty"`                                     // 执行状态（结果）
	TotalCase     int64                  `protobuf:"varint,22,opt,name=total_case,json=totalCase,proto3" json:"total_case,omitempty"`             // 总的用例数
	SuccessCase   int64                  `protobuf:"varint,23,opt,name=success_case,json=successCase,proto3" json:"success_case,omitempty"`       // 执行成功的用例数
	FailureCase   int64                  `protobuf:"varint,24,opt,name=failure_case,json=failureCase,proto3" json:"failure_case,omitempty"`       // 执行失败的用例数
	CostTime      int64                  `protobuf:"varint,25,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                // 耗时
	StartedAt     int64                  `protobuf:"varint,26,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`             // 开始时间
	EndedAt       int64                  `protobuf:"varint,27,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`                   // 结束时间
	ExecutedBy    string                 `protobuf:"bytes,28,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`           // 执行者
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UIDeviceRecord) Reset() {
	*x = UIDeviceRecord{}
	mi := &file_reporter_uireporter_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIDeviceRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIDeviceRecord) ProtoMessage() {}

func (x *UIDeviceRecord) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_uireporter_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIDeviceRecord.ProtoReflect.Descriptor instead.
func (*UIDeviceRecord) Descriptor() ([]byte, []int) {
	return file_reporter_uireporter_proto_rawDescGZIP(), []int{4}
}

func (x *UIDeviceRecord) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *UIDeviceRecord) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *UIDeviceRecord) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UIDeviceRecord) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *UIDeviceRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UIDeviceRecord) GetTotalCase() int64 {
	if x != nil {
		return x.TotalCase
	}
	return 0
}

func (x *UIDeviceRecord) GetSuccessCase() int64 {
	if x != nil {
		return x.SuccessCase
	}
	return 0
}

func (x *UIDeviceRecord) GetFailureCase() int64 {
	if x != nil {
		return x.FailureCase
	}
	return 0
}

func (x *UIDeviceRecord) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *UIDeviceRecord) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *UIDeviceRecord) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *UIDeviceRecord) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

var File_reporter_uireporter_proto protoreflect.FileDescriptor

var file_reporter_uireporter_proto_rawDesc = []byte{
	0x0a, 0x19, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2f, 0x75, 0x69, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x72, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe4, 0x09,
	0x0a, 0x0c, 0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x33, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x30, 0x0a, 0x0a, 0x67, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x69, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x67, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x5f, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c,
	0x69, 0x6e, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x39, 0x0a, 0x0d, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x74, 0x65,
	0x73, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74, 0x65, 0x73, 0x74, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3c,
	0x0a, 0x0e, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x0d, 0x74,
	0x65, 0x73, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1b, 0x0a, 0x09,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x72, 0x67, 0x73, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x65, 0x73, 0x74, 0x41, 0x72, 0x67, 0x73, 0x12, 0x33, 0x0a, 0x15, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x67, 0x65,
	0x74, 0x68, 0x65, 0x72, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x74, 0x6f, 0x67, 0x65,
	0x74, 0x68, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x29,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x18, 0x2a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x75, 0x69, 0x74, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x18, 0x2b,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x75, 0x69,
	0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x75,
	0x69, 0x74, 0x65, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x53, 0x75, 0x69, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x43, 0x61, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43, 0x61, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x30, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x63, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x31, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x32, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x22, 0xd1, 0x03, 0x0a, 0x0d, 0x55, 0x49, 0x53, 0x75, 0x69, 0x74, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x26,
	0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x69, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75,
	0x64, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x61, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x73, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x18, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43, 0x61, 0x73, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x19, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x42, 0x79, 0x22, 0xe9, 0x02, 0x0a, 0x0c, 0x55, 0x49, 0x43,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x69,
	0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x75, 0x64, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x63, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x22, 0xfd, 0x01, 0x0a, 0x0a, 0x55, 0x49, 0x43, 0x61, 0x73, 0x65, 0x53,
	0x74, 0x65, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x73, 0x74, 0x65, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x65, 0x70, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x22, 0xf9, 0x02, 0x0a, 0x0e, 0x55, 0x49, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x73,
	0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x61,
	0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61,
	0x73, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x43, 0x61, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x43, 0x61, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x73, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x73,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x1c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x42, 0x42, 0x5a, 0x40, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79,
	0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_reporter_uireporter_proto_rawDescOnce sync.Once
	file_reporter_uireporter_proto_rawDescData = file_reporter_uireporter_proto_rawDesc
)

func file_reporter_uireporter_proto_rawDescGZIP() []byte {
	file_reporter_uireporter_proto_rawDescOnce.Do(func() {
		file_reporter_uireporter_proto_rawDescData = protoimpl.X.CompressGZIP(file_reporter_uireporter_proto_rawDescData)
	})
	return file_reporter_uireporter_proto_rawDescData
}

var file_reporter_uireporter_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_reporter_uireporter_proto_goTypes = []any{
	(*UIPlanRecord)(nil),   // 0: reporter.UIPlanRecord
	(*UISuiteRecord)(nil),  // 1: reporter.UISuiteRecord
	(*UICaseRecord)(nil),   // 2: reporter.UICaseRecord
	(*UICaseStep)(nil),     // 3: reporter.UICaseStep
	(*UIDeviceRecord)(nil), // 4: reporter.UIDeviceRecord
	(pb.TriggerMode)(0),    // 5: common.TriggerMode
	(pb.PriorityType)(0),   // 6: common.PriorityType
	(pb.DeviceType)(0),     // 7: common.DeviceType
	(pb.PlatformType)(0),   // 8: common.PlatformType
	(*pb.GitConfig)(nil),   // 9: common.GitConfig
	(pb.TestLanguage)(0),   // 10: common.TestLanguage
	(pb.TestFramework)(0),  // 11: common.TestFramework
	(pb.TestStage)(0),      // 12: common.TestStage
}
var file_reporter_uireporter_proto_depIdxs = []int32{
	5,  // 0: reporter.UIPlanRecord.type:type_name -> common.TriggerMode
	6,  // 1: reporter.UIPlanRecord.priority_type:type_name -> common.PriorityType
	7,  // 2: reporter.UIPlanRecord.device_type:type_name -> common.DeviceType
	8,  // 3: reporter.UIPlanRecord.platform_type:type_name -> common.PlatformType
	9,  // 4: reporter.UIPlanRecord.git_config:type_name -> common.GitConfig
	10, // 5: reporter.UIPlanRecord.test_language:type_name -> common.TestLanguage
	11, // 6: reporter.UIPlanRecord.test_framework:type_name -> common.TestFramework
	12, // 7: reporter.UICaseStep.stage:type_name -> common.TestStage
	8,  // [8:8] is the sub-list for method output_type
	8,  // [8:8] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_reporter_uireporter_proto_init() }
func file_reporter_uireporter_proto_init() {
	if File_reporter_uireporter_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_reporter_uireporter_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_reporter_uireporter_proto_goTypes,
		DependencyIndexes: file_reporter_uireporter_proto_depIdxs,
		MessageInfos:      file_reporter_uireporter_proto_msgTypes,
	}.Build()
	File_reporter_uireporter_proto = out.File
	file_reporter_uireporter_proto_rawDesc = nil
	file_reporter_uireporter_proto_goTypes = nil
	file_reporter_uireporter_proto_depIdxs = nil
}
