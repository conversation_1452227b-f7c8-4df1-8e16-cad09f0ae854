// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: reporter/ui_agent_reporter.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UIAgentComponentRecordItem struct {
	state             protoimpl.MessageState          `protogen:"open.v1"`
	TaskId            string                          `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                          // 任务ID
	ExecuteId         string                          `protobuf:"bytes,2,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                                 // 执行ID
	ParentExecuteId   string                          `protobuf:"bytes,3,opt,name=parent_execute_id,json=parentExecuteId,proto3" json:"parent_execute_id,omitempty"`             // 父执行ID
	ProjectId         string                          `protobuf:"bytes,11,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                // 项目ID
	ComponentId       string                          `protobuf:"bytes,12,opt,name=component_id,json=componentId,proto3" json:"component_id,omitempty"`                          // 组件ID
	ComponentName     string                          `protobuf:"bytes,13,opt,name=component_name,json=componentName,proto3" json:"component_name,omitempty"`                    // 组件名称
	TriggerMode       pb.TriggerMode                  `protobuf:"varint,14,opt,name=trigger_mode,json=triggerMode,proto3,enum=common.TriggerMode" json:"trigger_mode,omitempty"` // 触发模式
	ExecuteType       pb.ExecuteType                  `protobuf:"varint,15,opt,name=execute_type,json=executeType,proto3,enum=common.ExecuteType" json:"execute_type,omitempty"` // 执行类型
	ApplicationConfig *pb.ApplicationConfig           `protobuf:"bytes,21,opt,name=application_config,json=applicationConfig,proto3" json:"application_config,omitempty"`        // 应用配置
	Mode              pb.UIAgentMode                  `protobuf:"varint,22,opt,name=mode,proto3,enum=common.UIAgentMode" json:"mode,omitempty"`                                  // 模式（Agent模式、Step模式）
	Steps             []*pb.UIAgentComponentStep      `protobuf:"bytes,23,rep,name=steps,proto3" json:"steps,omitempty"`                                                         // 步骤列表
	Expectation       *pb.UIAgentComponentExpectation `protobuf:"bytes,24,opt,name=expectation,proto3" json:"expectation,omitempty"`                                             // 期望结果
	// Deprecated: Marked as deprecated in reporter/ui_agent_reporter.proto.
	Variables        []*pb.GeneralConfigVar       `protobuf:"bytes,25,rep,name=variables,proto3" json:"variables,omitempty"`                                       // 变量列表
	InputParameters  []*pb.UIAgentInputParameter  `protobuf:"bytes,26,rep,name=input_parameters,json=inputParameters,proto3" json:"input_parameters,omitempty"`    // 入参列表
	OutputParameters []*pb.UIAgentOutputParameter `protobuf:"bytes,27,rep,name=output_parameters,json=outputParameters,proto3" json:"output_parameters,omitempty"` // 出参列表
	ForegroundCheck  bool                         `protobuf:"varint,28,opt,name=foreground_check,json=foregroundCheck,proto3" json:"foreground_check,omitempty"`   // 是否检查App在前台
	Device           *pb.UIAgentDevice            `protobuf:"bytes,29,opt,name=device,proto3" json:"device,omitempty"`                                             // 设备信息
	Reinstall        bool                         `protobuf:"varint,30,opt,name=reinstall,proto3" json:"reinstall,omitempty"`                                      // 是否重新安装
	Restart          bool                         `protobuf:"varint,31,opt,name=restart,proto3" json:"restart,omitempty"`                                          // 是否重启应用
	ReferenceId      string                       `protobuf:"bytes,32,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`                // 参考配置ID
	Times            int32                        `protobuf:"varint,33,opt,name=times,proto3" json:"times,omitempty"`                                              // 执行次数
	ExecutedSteps    int32                        `protobuf:"varint,41,opt,name=executed_steps,json=executedSteps,proto3" json:"executed_steps,omitempty"`         // 执行步骤数
	Successes        int32                        `protobuf:"varint,42,opt,name=successes,proto3" json:"successes,omitempty"`                                      // 执行成功次数
	Passes           int32                        `protobuf:"varint,43,opt,name=passes,proto3" json:"passes,omitempty"`                                            // 执行通过次数
	Status           string                       `protobuf:"bytes,44,opt,name=status,proto3" json:"status,omitempty"`                                             // 执行状态
	ExecutedBy       string                       `protobuf:"bytes,45,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`                   // 执行者
	StartedAt        int64                        `protobuf:"varint,46,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`                     // 开始时间
	EndedAt          int64                        `protobuf:"varint,47,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`                           // 结束时间
	CostTime         int64                        `protobuf:"varint,48,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                        // 执行耗时
	ErrMsg           *ErrorMessage                `protobuf:"bytes,49,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`                               // 错误信息
	Cleaned          bool                         `protobuf:"varint,91,opt,name=cleaned,proto3" json:"cleaned,omitempty"`                                          // 是否已被清理
	CreatedAt        int64                        `protobuf:"varint,92,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                     // 创建时间
	UpdatedAt        int64                        `protobuf:"varint,93,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                     // 更新时间
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UIAgentComponentRecordItem) Reset() {
	*x = UIAgentComponentRecordItem{}
	mi := &file_reporter_ui_agent_reporter_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentComponentRecordItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentComponentRecordItem) ProtoMessage() {}

func (x *UIAgentComponentRecordItem) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_ui_agent_reporter_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentComponentRecordItem.ProtoReflect.Descriptor instead.
func (*UIAgentComponentRecordItem) Descriptor() ([]byte, []int) {
	return file_reporter_ui_agent_reporter_proto_rawDescGZIP(), []int{0}
}

func (x *UIAgentComponentRecordItem) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *UIAgentComponentRecordItem) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *UIAgentComponentRecordItem) GetParentExecuteId() string {
	if x != nil {
		return x.ParentExecuteId
	}
	return ""
}

func (x *UIAgentComponentRecordItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UIAgentComponentRecordItem) GetComponentId() string {
	if x != nil {
		return x.ComponentId
	}
	return ""
}

func (x *UIAgentComponentRecordItem) GetComponentName() string {
	if x != nil {
		return x.ComponentName
	}
	return ""
}

func (x *UIAgentComponentRecordItem) GetTriggerMode() pb.TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return pb.TriggerMode(0)
}

func (x *UIAgentComponentRecordItem) GetExecuteType() pb.ExecuteType {
	if x != nil {
		return x.ExecuteType
	}
	return pb.ExecuteType(0)
}

func (x *UIAgentComponentRecordItem) GetApplicationConfig() *pb.ApplicationConfig {
	if x != nil {
		return x.ApplicationConfig
	}
	return nil
}

func (x *UIAgentComponentRecordItem) GetMode() pb.UIAgentMode {
	if x != nil {
		return x.Mode
	}
	return pb.UIAgentMode(0)
}

func (x *UIAgentComponentRecordItem) GetSteps() []*pb.UIAgentComponentStep {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *UIAgentComponentRecordItem) GetExpectation() *pb.UIAgentComponentExpectation {
	if x != nil {
		return x.Expectation
	}
	return nil
}

// Deprecated: Marked as deprecated in reporter/ui_agent_reporter.proto.
func (x *UIAgentComponentRecordItem) GetVariables() []*pb.GeneralConfigVar {
	if x != nil {
		return x.Variables
	}
	return nil
}

func (x *UIAgentComponentRecordItem) GetInputParameters() []*pb.UIAgentInputParameter {
	if x != nil {
		return x.InputParameters
	}
	return nil
}

func (x *UIAgentComponentRecordItem) GetOutputParameters() []*pb.UIAgentOutputParameter {
	if x != nil {
		return x.OutputParameters
	}
	return nil
}

func (x *UIAgentComponentRecordItem) GetForegroundCheck() bool {
	if x != nil {
		return x.ForegroundCheck
	}
	return false
}

func (x *UIAgentComponentRecordItem) GetDevice() *pb.UIAgentDevice {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *UIAgentComponentRecordItem) GetReinstall() bool {
	if x != nil {
		return x.Reinstall
	}
	return false
}

func (x *UIAgentComponentRecordItem) GetRestart() bool {
	if x != nil {
		return x.Restart
	}
	return false
}

func (x *UIAgentComponentRecordItem) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *UIAgentComponentRecordItem) GetTimes() int32 {
	if x != nil {
		return x.Times
	}
	return 0
}

func (x *UIAgentComponentRecordItem) GetExecutedSteps() int32 {
	if x != nil {
		return x.ExecutedSteps
	}
	return 0
}

func (x *UIAgentComponentRecordItem) GetSuccesses() int32 {
	if x != nil {
		return x.Successes
	}
	return 0
}

func (x *UIAgentComponentRecordItem) GetPasses() int32 {
	if x != nil {
		return x.Passes
	}
	return 0
}

func (x *UIAgentComponentRecordItem) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UIAgentComponentRecordItem) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

func (x *UIAgentComponentRecordItem) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *UIAgentComponentRecordItem) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *UIAgentComponentRecordItem) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *UIAgentComponentRecordItem) GetErrMsg() *ErrorMessage {
	if x != nil {
		return x.ErrMsg
	}
	return nil
}

func (x *UIAgentComponentRecordItem) GetCleaned() bool {
	if x != nil {
		return x.Cleaned
	}
	return false
}

func (x *UIAgentComponentRecordItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UIAgentComponentRecordItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_reporter_ui_agent_reporter_proto protoreflect.FileDescriptor

var file_reporter_ui_agent_reporter_proto_rawDesc = []byte{
	0x0a, 0x20, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2f, 0x75, 0x69, 0x5f, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x1a, 0x13, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x5f,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xdb, 0x0a, 0x0a, 0x1a, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x36,
	0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x48,
	0x0a, 0x12, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x27, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d, 0x6f, 0x64,
	0x65, 0x12, 0x32, 0x0a, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x65, 0x70, 0x52, 0x05,
	0x73, 0x74, 0x65, 0x70, 0x73, 0x12, 0x45, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0b, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x09,
	0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x61, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x76,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x10, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x1a, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x49, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x52, 0x0f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x12, 0x4b, 0x0a, 0x11, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x10, 0x6f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12,
	0x29, 0x0a, 0x10, 0x66, 0x6f, 0x72, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x66, 0x6f, 0x72, 0x65, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x2d, 0x0a, 0x06, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x72, 0x65,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x21, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x29, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x53, 0x74, 0x65, 0x70,
	0x73, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x2a,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x70, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x70, 0x61, 0x73, 0x73, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x2d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x2e,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x2f, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f,
	0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x30, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63,
	0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x5f, 0x6d,
	0x73, 0x67, 0x18, 0x31, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x65, 0x61,
	0x6e, 0x65, 0x64, 0x18, 0x5b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x63, 0x6c, 0x65, 0x61, 0x6e,
	0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x5c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x5d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x42, 0x42, 0x5a, 0x40, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79,
	0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_reporter_ui_agent_reporter_proto_rawDescOnce sync.Once
	file_reporter_ui_agent_reporter_proto_rawDescData = file_reporter_ui_agent_reporter_proto_rawDesc
)

func file_reporter_ui_agent_reporter_proto_rawDescGZIP() []byte {
	file_reporter_ui_agent_reporter_proto_rawDescOnce.Do(func() {
		file_reporter_ui_agent_reporter_proto_rawDescData = protoimpl.X.CompressGZIP(file_reporter_ui_agent_reporter_proto_rawDescData)
	})
	return file_reporter_ui_agent_reporter_proto_rawDescData
}

var file_reporter_ui_agent_reporter_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_reporter_ui_agent_reporter_proto_goTypes = []any{
	(*UIAgentComponentRecordItem)(nil),     // 0: reporter.UIAgentComponentRecordItem
	(pb.TriggerMode)(0),                    // 1: common.TriggerMode
	(pb.ExecuteType)(0),                    // 2: common.ExecuteType
	(*pb.ApplicationConfig)(nil),           // 3: common.ApplicationConfig
	(pb.UIAgentMode)(0),                    // 4: common.UIAgentMode
	(*pb.UIAgentComponentStep)(nil),        // 5: common.UIAgentComponentStep
	(*pb.UIAgentComponentExpectation)(nil), // 6: common.UIAgentComponentExpectation
	(*pb.GeneralConfigVar)(nil),            // 7: common.GeneralConfigVar
	(*pb.UIAgentInputParameter)(nil),       // 8: common.UIAgentInputParameter
	(*pb.UIAgentOutputParameter)(nil),      // 9: common.UIAgentOutputParameter
	(*pb.UIAgentDevice)(nil),               // 10: common.UIAgentDevice
	(*ErrorMessage)(nil),                   // 11: reporter.ErrorMessage
}
var file_reporter_ui_agent_reporter_proto_depIdxs = []int32{
	1,  // 0: reporter.UIAgentComponentRecordItem.trigger_mode:type_name -> common.TriggerMode
	2,  // 1: reporter.UIAgentComponentRecordItem.execute_type:type_name -> common.ExecuteType
	3,  // 2: reporter.UIAgentComponentRecordItem.application_config:type_name -> common.ApplicationConfig
	4,  // 3: reporter.UIAgentComponentRecordItem.mode:type_name -> common.UIAgentMode
	5,  // 4: reporter.UIAgentComponentRecordItem.steps:type_name -> common.UIAgentComponentStep
	6,  // 5: reporter.UIAgentComponentRecordItem.expectation:type_name -> common.UIAgentComponentExpectation
	7,  // 6: reporter.UIAgentComponentRecordItem.variables:type_name -> common.GeneralConfigVar
	8,  // 7: reporter.UIAgentComponentRecordItem.input_parameters:type_name -> common.UIAgentInputParameter
	9,  // 8: reporter.UIAgentComponentRecordItem.output_parameters:type_name -> common.UIAgentOutputParameter
	10, // 9: reporter.UIAgentComponentRecordItem.device:type_name -> common.UIAgentDevice
	11, // 10: reporter.UIAgentComponentRecordItem.err_msg:type_name -> reporter.ErrorMessage
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_reporter_ui_agent_reporter_proto_init() }
func file_reporter_ui_agent_reporter_proto_init() {
	if File_reporter_ui_agent_reporter_proto != nil {
		return
	}
	file_reporter_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_reporter_ui_agent_reporter_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_reporter_ui_agent_reporter_proto_goTypes,
		DependencyIndexes: file_reporter_ui_agent_reporter_proto_depIdxs,
		MessageInfos:      file_reporter_ui_agent_reporter_proto_msgTypes,
	}.Build()
	File_reporter_ui_agent_reporter_proto = out.File
	file_reporter_ui_agent_reporter_proto_rawDesc = nil
	file_reporter_ui_agent_reporter_proto_goTypes = nil
	file_reporter_ui_agent_reporter_proto_depIdxs = nil
}
