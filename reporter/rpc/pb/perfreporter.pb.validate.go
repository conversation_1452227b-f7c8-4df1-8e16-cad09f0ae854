// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: reporter/perfreporter.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.TriggerMode(0)
)

// Validate checks the field values on PerfPlanRecord with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfPlanRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfPlanRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfPlanRecordMultiError,
// or nil if none found.
func (m *PerfPlanRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfPlanRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for PlanName

	// no validation rules for TriggerMode

	// no validation rules for TargetMaxRps

	// no validation rules for TargetDuration

	// no validation rules for Protocol

	// no validation rules for TargetEnv

	// no validation rules for Status

	// no validation rules for TaskType

	// no validation rules for ExecutionMode

	for idx, item := range m.GetServices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfPlanRecordValidationError{
						field:  fmt.Sprintf("Services[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfPlanRecordValidationError{
						field:  fmt.Sprintf("Services[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfPlanRecordValidationError{
					field:  fmt.Sprintf("Services[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CostTime

	for idx, item := range m.GetMonitorUrls() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfPlanRecordValidationError{
						field:  fmt.Sprintf("MonitorUrls[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfPlanRecordValidationError{
						field:  fmt.Sprintf("MonitorUrls[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfPlanRecordValidationError{
					field:  fmt.Sprintf("MonitorUrls[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ExecutedBy

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	for idx, item := range m.GetApiMetrics() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfPlanRecordValidationError{
						field:  fmt.Sprintf("ApiMetrics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfPlanRecordValidationError{
						field:  fmt.Sprintf("ApiMetrics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfPlanRecordValidationError{
					field:  fmt.Sprintf("ApiMetrics[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetErrMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfPlanRecordValidationError{
					field:  "ErrMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfPlanRecordValidationError{
					field:  "ErrMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfPlanRecordValidationError{
				field:  "ErrMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return PerfPlanRecordMultiError(errors)
	}

	return nil
}

// PerfPlanRecordMultiError is an error wrapping multiple validation errors
// returned by PerfPlanRecord.ValidateAll() if the designated constraints
// aren't met.
type PerfPlanRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfPlanRecordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfPlanRecordMultiError) AllErrors() []error { return m }

// PerfPlanRecordValidationError is the validation error returned by
// PerfPlanRecord.Validate if the designated constraints aren't met.
type PerfPlanRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfPlanRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfPlanRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfPlanRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfPlanRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfPlanRecordValidationError) ErrorName() string { return "PerfPlanRecordValidationError" }

// Error satisfies the builtin error interface
func (e PerfPlanRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfPlanRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfPlanRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfPlanRecordValidationError{}

// Validate checks the field values on PerfSuiteRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PerfSuiteRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfSuiteRecord with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfSuiteRecordMultiError, or nil if none found.
func (m *PerfSuiteRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfSuiteRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for PlanExecuteId

	// no validation rules for ProjectId

	// no validation rules for SuiteId

	// no validation rules for SuiteName

	// no validation rules for Status

	// no validation rules for CostTime

	// no validation rules for ExecutedBy

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	if all {
		switch v := interface{}(m.GetErrMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfSuiteRecordValidationError{
					field:  "ErrMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfSuiteRecordValidationError{
					field:  "ErrMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfSuiteRecordValidationError{
				field:  "ErrMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return PerfSuiteRecordMultiError(errors)
	}

	return nil
}

// PerfSuiteRecordMultiError is an error wrapping multiple validation errors
// returned by PerfSuiteRecord.ValidateAll() if the designated constraints
// aren't met.
type PerfSuiteRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfSuiteRecordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfSuiteRecordMultiError) AllErrors() []error { return m }

// PerfSuiteRecordValidationError is the validation error returned by
// PerfSuiteRecord.Validate if the designated constraints aren't met.
type PerfSuiteRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfSuiteRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfSuiteRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfSuiteRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfSuiteRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfSuiteRecordValidationError) ErrorName() string { return "PerfSuiteRecordValidationError" }

// Error satisfies the builtin error interface
func (e PerfSuiteRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfSuiteRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfSuiteRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfSuiteRecordValidationError{}

// Validate checks the field values on PerfCaseRecord with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfCaseRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCaseRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfCaseRecordMultiError,
// or nil if none found.
func (m *PerfCaseRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCaseRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for SuiteExecuteId

	// no validation rules for PlanExecuteId

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for PlanName

	// no validation rules for SuiteId

	// no validation rules for SuiteName

	// no validation rules for CaseId

	// no validation rules for CaseName

	for idx, item := range m.GetSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfCaseRecordValidationError{
						field:  fmt.Sprintf("Steps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfCaseRecordValidationError{
						field:  fmt.Sprintf("Steps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfCaseRecordValidationError{
					field:  fmt.Sprintf("Steps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPerfData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfCaseRecordValidationError{
					field:  "PerfData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfCaseRecordValidationError{
					field:  "PerfData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerfData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfCaseRecordValidationError{
				field:  "PerfData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoadGenerator()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfCaseRecordValidationError{
					field:  "LoadGenerator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfCaseRecordValidationError{
					field:  "LoadGenerator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoadGenerator()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfCaseRecordValidationError{
				field:  "LoadGenerator",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for CostTime

	// no validation rules for ExecutedBy

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	for idx, item := range m.GetApiMetrics() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfCaseRecordValidationError{
						field:  fmt.Sprintf("ApiMetrics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfCaseRecordValidationError{
						field:  fmt.Sprintf("ApiMetrics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfCaseRecordValidationError{
					field:  fmt.Sprintf("ApiMetrics[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetErrMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PerfCaseRecordValidationError{
					field:  "ErrMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PerfCaseRecordValidationError{
					field:  "ErrMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PerfCaseRecordValidationError{
				field:  "ErrMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return PerfCaseRecordMultiError(errors)
	}

	return nil
}

// PerfCaseRecordMultiError is an error wrapping multiple validation errors
// returned by PerfCaseRecord.ValidateAll() if the designated constraints
// aren't met.
type PerfCaseRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCaseRecordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCaseRecordMultiError) AllErrors() []error { return m }

// PerfCaseRecordValidationError is the validation error returned by
// PerfCaseRecord.Validate if the designated constraints aren't met.
type PerfCaseRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCaseRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCaseRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCaseRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCaseRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCaseRecordValidationError) ErrorName() string { return "PerfCaseRecordValidationError" }

// Error satisfies the builtin error interface
func (e PerfCaseRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCaseRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCaseRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCaseRecordValidationError{}

// Validate checks the field values on PerfCaseStepInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PerfCaseStepInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfCaseStepInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfCaseStepInfoMultiError, or nil if none found.
func (m *PerfCaseStepInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfCaseStepInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetName()); l < 1 || l > 64 {
		err := PerfCaseStepInfoValidationError{
			field:  "Name",
			reason: "value length must be between 1 and 64 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _PerfCaseStepInfo_Type_NotInLookup[m.GetType()]; ok {
		err := PerfCaseStepInfoValidationError{
			field:  "Type",
			reason: "value must not be in list [PerfCaseStepType_NULL]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetRateLimits()) < 1 {
		err := PerfCaseStepInfoValidationError{
			field:  "RateLimits",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetRateLimits() {
		_, _ = idx, item

		if item == nil {
			err := PerfCaseStepInfoValidationError{
				field:  fmt.Sprintf("RateLimits[%v]", idx),
				reason: "value is required",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfCaseStepInfoValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfCaseStepInfoValidationError{
						field:  fmt.Sprintf("RateLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfCaseStepInfoValidationError{
					field:  fmt.Sprintf("RateLimits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if utf8.RuneCountInString(m.GetApiName()) > 255 {
		err := PerfCaseStepInfoValidationError{
			field:  "ApiName",
			reason: "value length must be at most 255 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return PerfCaseStepInfoMultiError(errors)
	}

	return nil
}

// PerfCaseStepInfoMultiError is an error wrapping multiple validation errors
// returned by PerfCaseStepInfo.ValidateAll() if the designated constraints
// aren't met.
type PerfCaseStepInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfCaseStepInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfCaseStepInfoMultiError) AllErrors() []error { return m }

// PerfCaseStepInfoValidationError is the validation error returned by
// PerfCaseStepInfo.Validate if the designated constraints aren't met.
type PerfCaseStepInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfCaseStepInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfCaseStepInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfCaseStepInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfCaseStepInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfCaseStepInfoValidationError) ErrorName() string { return "PerfCaseStepInfoValidationError" }

// Error satisfies the builtin error interface
func (e PerfCaseStepInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfCaseStepInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfCaseStepInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfCaseStepInfoValidationError{}

var _PerfCaseStepInfo_Type_NotInLookup = map[pb.PerfCaseStepType]struct{}{
	0: {},
}

// Validate checks the field values on PerfDataInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfDataInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfDataInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfDataInfoMultiError, or
// nil if none found.
func (m *PerfDataInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfDataInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_PerfDataInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := PerfDataInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDataId() != "" {

		if !_PerfDataInfo_DataId_Pattern.MatchString(m.GetDataId()) {
			err := PerfDataInfoValidationError{
				field:  "DataId",
				reason: "value does not match regex pattern \"(?:^perf_data_id:.+?)\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetName() != "" {

		if utf8.RuneCountInString(m.GetName()) > 64 {
			err := PerfDataInfoValidationError{
				field:  "Name",
				reason: "value length must be at most 64 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.GetNumberOfVu() <= 0 {
		err := PerfDataInfoValidationError{
			field:  "NumberOfVu",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return PerfDataInfoMultiError(errors)
	}

	return nil
}

// PerfDataInfoMultiError is an error wrapping multiple validation errors
// returned by PerfDataInfo.ValidateAll() if the designated constraints aren't met.
type PerfDataInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfDataInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfDataInfoMultiError) AllErrors() []error { return m }

// PerfDataInfoValidationError is the validation error returned by
// PerfDataInfo.Validate if the designated constraints aren't met.
type PerfDataInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfDataInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfDataInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfDataInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfDataInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfDataInfoValidationError) ErrorName() string { return "PerfDataInfoValidationError" }

// Error satisfies the builtin error interface
func (e PerfDataInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfDataInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfDataInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfDataInfoValidationError{}

var _PerfDataInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _PerfDataInfo_DataId_Pattern = regexp.MustCompile("(?:^perf_data_id:.+?)")

// Validate checks the field values on MonitorUrl with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MonitorUrl) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MonitorUrl with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MonitorUrlMultiError, or
// nil if none found.
func (m *MonitorUrl) ValidateAll() error {
	return m.validate(true)
}

func (m *MonitorUrl) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Url

	if len(errors) > 0 {
		return MonitorUrlMultiError(errors)
	}

	return nil
}

// MonitorUrlMultiError is an error wrapping multiple validation errors
// returned by MonitorUrl.ValidateAll() if the designated constraints aren't met.
type MonitorUrlMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MonitorUrlMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MonitorUrlMultiError) AllErrors() []error { return m }

// MonitorUrlValidationError is the validation error returned by
// MonitorUrl.Validate if the designated constraints aren't met.
type MonitorUrlValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MonitorUrlValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MonitorUrlValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MonitorUrlValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MonitorUrlValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MonitorUrlValidationError) ErrorName() string { return "MonitorUrlValidationError" }

// Error satisfies the builtin error interface
func (e MonitorUrlValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMonitorUrl.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MonitorUrlValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MonitorUrlValidationError{}

// Validate checks the field values on APIMetric with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *APIMetric) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on APIMetric with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in APIMetricMultiError, or nil
// if none found.
func (m *APIMetric) ValidateAll() error {
	return m.validate(true)
}

func (m *APIMetric) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApiName

	// no validation rules for ReqSuccessful

	// no validation rules for ReqFailed

	for idx, item := range m.GetRespSuites() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, APIMetricValidationError{
						field:  fmt.Sprintf("RespSuites[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, APIMetricValidationError{
						field:  fmt.Sprintf("RespSuites[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return APIMetricValidationError{
					field:  fmt.Sprintf("RespSuites[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return APIMetricMultiError(errors)
	}

	return nil
}

// APIMetricMultiError is an error wrapping multiple validation errors returned
// by APIMetric.ValidateAll() if the designated constraints aren't met.
type APIMetricMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m APIMetricMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m APIMetricMultiError) AllErrors() []error { return m }

// APIMetricValidationError is the validation error returned by
// APIMetric.Validate if the designated constraints aren't met.
type APIMetricValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e APIMetricValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e APIMetricValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e APIMetricValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e APIMetricValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e APIMetricValidationError) ErrorName() string { return "APIMetricValidationError" }

// Error satisfies the builtin error interface
func (e APIMetricValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAPIMetric.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = APIMetricValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = APIMetricValidationError{}

// Validate checks the field values on SearchPerfPlanRecordItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchPerfPlanRecordItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchPerfPlanRecordItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchPerfPlanRecordItemMultiError, or nil if none found.
func (m *SearchPerfPlanRecordItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchPerfPlanRecordItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for PlanName

	// no validation rules for TriggerMode

	// no validation rules for TargetMaxRps

	// no validation rules for TargetDuration

	// no validation rules for Protocol

	// no validation rules for TargetEnv

	// no validation rules for Status

	// no validation rules for TaskType

	// no validation rules for ExecutionMode

	// no validation rules for CostTime

	// no validation rules for HasMonitorUrl

	// no validation rules for ExecutedBy

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	if all {
		switch v := interface{}(m.GetErrMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchPerfPlanRecordItemValidationError{
					field:  "ErrMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchPerfPlanRecordItemValidationError{
					field:  "ErrMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchPerfPlanRecordItemValidationError{
				field:  "ErrMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchPerfPlanRecordItemMultiError(errors)
	}

	return nil
}

// SearchPerfPlanRecordItemMultiError is an error wrapping multiple validation
// errors returned by SearchPerfPlanRecordItem.ValidateAll() if the designated
// constraints aren't met.
type SearchPerfPlanRecordItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchPerfPlanRecordItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchPerfPlanRecordItemMultiError) AllErrors() []error { return m }

// SearchPerfPlanRecordItemValidationError is the validation error returned by
// SearchPerfPlanRecordItem.Validate if the designated constraints aren't met.
type SearchPerfPlanRecordItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchPerfPlanRecordItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchPerfPlanRecordItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchPerfPlanRecordItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchPerfPlanRecordItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchPerfPlanRecordItemValidationError) ErrorName() string {
	return "SearchPerfPlanRecordItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchPerfPlanRecordItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchPerfPlanRecordItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchPerfPlanRecordItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchPerfPlanRecordItemValidationError{}

// Validate checks the field values on APIMetric_ResponseResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *APIMetric_ResponseResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on APIMetric_ResponseResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// APIMetric_ResponseResultMultiError, or nil if none found.
func (m *APIMetric_ResponseResult) ValidateAll() error {
	return m.validate(true)
}

func (m *APIMetric_ResponseResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for Count

	// no validation rules for Sum

	for idx, item := range m.GetQuantiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, APIMetric_ResponseResultValidationError{
						field:  fmt.Sprintf("Quantiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, APIMetric_ResponseResultValidationError{
						field:  fmt.Sprintf("Quantiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return APIMetric_ResponseResultValidationError{
					field:  fmt.Sprintf("Quantiles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetBuckets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, APIMetric_ResponseResultValidationError{
						field:  fmt.Sprintf("Buckets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, APIMetric_ResponseResultValidationError{
						field:  fmt.Sprintf("Buckets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return APIMetric_ResponseResultValidationError{
					field:  fmt.Sprintf("Buckets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return APIMetric_ResponseResultMultiError(errors)
	}

	return nil
}

// APIMetric_ResponseResultMultiError is an error wrapping multiple validation
// errors returned by APIMetric_ResponseResult.ValidateAll() if the designated
// constraints aren't met.
type APIMetric_ResponseResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m APIMetric_ResponseResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m APIMetric_ResponseResultMultiError) AllErrors() []error { return m }

// APIMetric_ResponseResultValidationError is the validation error returned by
// APIMetric_ResponseResult.Validate if the designated constraints aren't met.
type APIMetric_ResponseResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e APIMetric_ResponseResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e APIMetric_ResponseResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e APIMetric_ResponseResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e APIMetric_ResponseResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e APIMetric_ResponseResultValidationError) ErrorName() string {
	return "APIMetric_ResponseResultValidationError"
}

// Error satisfies the builtin error interface
func (e APIMetric_ResponseResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAPIMetric_ResponseResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = APIMetric_ResponseResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = APIMetric_ResponseResultValidationError{}

// Validate checks the field values on APIMetric_ResponseResult_KeyValuePair
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *APIMetric_ResponseResult_KeyValuePair) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on APIMetric_ResponseResult_KeyValuePair
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// APIMetric_ResponseResult_KeyValuePairMultiError, or nil if none found.
func (m *APIMetric_ResponseResult_KeyValuePair) ValidateAll() error {
	return m.validate(true)
}

func (m *APIMetric_ResponseResult_KeyValuePair) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Key

	// no validation rules for Value

	if len(errors) > 0 {
		return APIMetric_ResponseResult_KeyValuePairMultiError(errors)
	}

	return nil
}

// APIMetric_ResponseResult_KeyValuePairMultiError is an error wrapping
// multiple validation errors returned by
// APIMetric_ResponseResult_KeyValuePair.ValidateAll() if the designated
// constraints aren't met.
type APIMetric_ResponseResult_KeyValuePairMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m APIMetric_ResponseResult_KeyValuePairMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m APIMetric_ResponseResult_KeyValuePairMultiError) AllErrors() []error { return m }

// APIMetric_ResponseResult_KeyValuePairValidationError is the validation error
// returned by APIMetric_ResponseResult_KeyValuePair.Validate if the
// designated constraints aren't met.
type APIMetric_ResponseResult_KeyValuePairValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e APIMetric_ResponseResult_KeyValuePairValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e APIMetric_ResponseResult_KeyValuePairValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e APIMetric_ResponseResult_KeyValuePairValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e APIMetric_ResponseResult_KeyValuePairValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e APIMetric_ResponseResult_KeyValuePairValidationError) ErrorName() string {
	return "APIMetric_ResponseResult_KeyValuePairValidationError"
}

// Error satisfies the builtin error interface
func (e APIMetric_ResponseResult_KeyValuePairValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAPIMetric_ResponseResult_KeyValuePair.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = APIMetric_ResponseResult_KeyValuePairValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = APIMetric_ResponseResult_KeyValuePairValidationError{}
