// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: reporter/uireporter.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.TriggerMode(0)
)

// Validate checks the field values on UIPlanRecord with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UIPlanRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIPlanRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UIPlanRecordMultiError, or
// nil if none found.
func (m *UIPlanRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *UIPlanRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for PlanName

	// no validation rules for Type

	// no validation rules for PriorityType

	// no validation rules for DeviceType

	// no validation rules for PlatformType

	if all {
		switch v := interface{}(m.GetGitConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIPlanRecordValidationError{
					field:  "GitConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIPlanRecordValidationError{
					field:  "GitConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGitConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIPlanRecordValidationError{
				field:  "GitConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PackageName

	// no validation rules for CallbackUrl

	// no validation rules for AppDownloadLink

	// no validation rules for AppVersion

	// no validation rules for AppName

	// no validation rules for TestLanguage

	// no validation rules for TestLanguageVersion

	// no validation rules for TestFramework

	// no validation rules for ExecutionEnvironment

	// no validation rules for Together

	// no validation rules for Status

	// no validation rules for TotalSuite

	// no validation rules for SuccessSuite

	// no validation rules for FailureSuite

	// no validation rules for TotalCase

	// no validation rules for SuccessCase

	// no validation rules for FailureCase

	// no validation rules for CostTime

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	// no validation rules for ExecutedBy

	if len(errors) > 0 {
		return UIPlanRecordMultiError(errors)
	}

	return nil
}

// UIPlanRecordMultiError is an error wrapping multiple validation errors
// returned by UIPlanRecord.ValidateAll() if the designated constraints aren't met.
type UIPlanRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIPlanRecordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIPlanRecordMultiError) AllErrors() []error { return m }

// UIPlanRecordValidationError is the validation error returned by
// UIPlanRecord.Validate if the designated constraints aren't met.
type UIPlanRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIPlanRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIPlanRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIPlanRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIPlanRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIPlanRecordValidationError) ErrorName() string { return "UIPlanRecordValidationError" }

// Error satisfies the builtin error interface
func (e UIPlanRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIPlanRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIPlanRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIPlanRecordValidationError{}

// Validate checks the field values on UISuiteRecord with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UISuiteRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UISuiteRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UISuiteRecordMultiError, or
// nil if none found.
func (m *UISuiteRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *UISuiteRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for PlanExecuteId

	// no validation rules for ProjectId

	// no validation rules for SuiteId

	// no validation rules for SuiteName

	// no validation rules for Udid

	// no validation rules for Status

	// no validation rules for TotalCase

	// no validation rules for SuccessCase

	// no validation rules for FailureCase

	// no validation rules for CostTime

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	// no validation rules for ExecutedBy

	if len(errors) > 0 {
		return UISuiteRecordMultiError(errors)
	}

	return nil
}

// UISuiteRecordMultiError is an error wrapping multiple validation errors
// returned by UISuiteRecord.ValidateAll() if the designated constraints
// aren't met.
type UISuiteRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UISuiteRecordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UISuiteRecordMultiError) AllErrors() []error { return m }

// UISuiteRecordValidationError is the validation error returned by
// UISuiteRecord.Validate if the designated constraints aren't met.
type UISuiteRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UISuiteRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UISuiteRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UISuiteRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UISuiteRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UISuiteRecordValidationError) ErrorName() string { return "UISuiteRecordValidationError" }

// Error satisfies the builtin error interface
func (e UISuiteRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUISuiteRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UISuiteRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UISuiteRecordValidationError{}

// Validate checks the field values on UICaseRecord with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UICaseRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UICaseRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UICaseRecordMultiError, or
// nil if none found.
func (m *UICaseRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *UICaseRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for SuiteExecuteId

	// no validation rules for ProjectId

	// no validation rules for CaseId

	// no validation rules for CaseName

	// no validation rules for Udid

	// no validation rules for Status

	// no validation rules for CostTime

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	// no validation rules for ExecutedBy

	if len(errors) > 0 {
		return UICaseRecordMultiError(errors)
	}

	return nil
}

// UICaseRecordMultiError is an error wrapping multiple validation errors
// returned by UICaseRecord.ValidateAll() if the designated constraints aren't met.
type UICaseRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UICaseRecordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UICaseRecordMultiError) AllErrors() []error { return m }

// UICaseRecordValidationError is the validation error returned by
// UICaseRecord.Validate if the designated constraints aren't met.
type UICaseRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UICaseRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UICaseRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UICaseRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UICaseRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UICaseRecordValidationError) ErrorName() string { return "UICaseRecordValidationError" }

// Error satisfies the builtin error interface
func (e UICaseRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUICaseRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UICaseRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UICaseRecordValidationError{}

// Validate checks the field values on UICaseStep with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UICaseStep) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UICaseStep with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UICaseStepMultiError, or
// nil if none found.
func (m *UICaseStep) ValidateAll() error {
	return m.validate(true)
}

func (m *UICaseStep) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for StepId

	// no validation rules for Stage

	// no validation rules for Name

	// no validation rules for Status

	// no validation rules for Content

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	// no validation rules for Index

	if len(errors) > 0 {
		return UICaseStepMultiError(errors)
	}

	return nil
}

// UICaseStepMultiError is an error wrapping multiple validation errors
// returned by UICaseStep.ValidateAll() if the designated constraints aren't met.
type UICaseStepMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UICaseStepMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UICaseStepMultiError) AllErrors() []error { return m }

// UICaseStepValidationError is the validation error returned by
// UICaseStep.Validate if the designated constraints aren't met.
type UICaseStepValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UICaseStepValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UICaseStepValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UICaseStepValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UICaseStepValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UICaseStepValidationError) ErrorName() string { return "UICaseStepValidationError" }

// Error satisfies the builtin error interface
func (e UICaseStepValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUICaseStep.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UICaseStepValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UICaseStepValidationError{}

// Validate checks the field values on UIDeviceRecord with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UIDeviceRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIDeviceRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UIDeviceRecordMultiError,
// or nil if none found.
func (m *UIDeviceRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *UIDeviceRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for PlanExecuteId

	// no validation rules for ProjectId

	// no validation rules for Udid

	// no validation rules for Status

	// no validation rules for TotalCase

	// no validation rules for SuccessCase

	// no validation rules for FailureCase

	// no validation rules for CostTime

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	// no validation rules for ExecutedBy

	if len(errors) > 0 {
		return UIDeviceRecordMultiError(errors)
	}

	return nil
}

// UIDeviceRecordMultiError is an error wrapping multiple validation errors
// returned by UIDeviceRecord.ValidateAll() if the designated constraints
// aren't met.
type UIDeviceRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIDeviceRecordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIDeviceRecordMultiError) AllErrors() []error { return m }

// UIDeviceRecordValidationError is the validation error returned by
// UIDeviceRecord.Validate if the designated constraints aren't met.
type UIDeviceRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIDeviceRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIDeviceRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIDeviceRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIDeviceRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIDeviceRecordValidationError) ErrorName() string { return "UIDeviceRecordValidationError" }

// Error satisfies the builtin error interface
func (e UIDeviceRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIDeviceRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIDeviceRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIDeviceRecordValidationError{}
