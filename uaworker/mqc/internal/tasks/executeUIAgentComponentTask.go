package tasks

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/mqc/internal/logic/uiAgentComponent"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/mqc/internal/svc"
)

var _ base.Handler = (*UIAgentComponentTaskProcessor)(nil)

type (
	UIAgentComponentTaskProcessor struct {
		svcCtx *svc.ServiceContext
	}

	taskInfo struct {
		Name    string `json:"name"`
		Payload []byte `json:"payload"`
		Queue   string `json:"queue"`

		FirstProcessedAt time.Time `json:"first_processed_at"` // 首次处理时间
		LastProcessedAt  time.Time `json:"last_processed_at"`  // 最后处理时间
		ProcessedTimes   int32     `json:"processed_times"`    // 处理次数
	}
)

func NewUIAgentComponentTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &UIAgentComponentTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *UIAgentComponentTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) ([]byte, error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v", r)
		}
	}()

	var req dispatcherpb.WorkerReq
	if err := protobuf.UnmarshalJSON(task.Payload, &req); err != nil {
		logger.Errorf(
			"failed to unmarshal the payload of ui agent component task, payload: %s, error: %+v", task.Payload, err,
		)
		return []byte(constants.FAILURE), nil
	}

	ctx = updateContext(
		ctx, req.GetTaskId(), req.GetExecuteId(), req.GetUiAgentComponent().GetParentExecuteId(), req.GetUser(),
	)
	p.setTaskInfoToCache(ctx, task, req.GetTaskId(), req.GetExecuteId())

	info, err := convertToTaskInfo(&req)
	if err != nil {
		logger.Errorf("failed to convert ui agent component task, payload: %s, error: %+v", task.Payload, err)
		return []byte(constants.FAILURE), nil
	}
	logger.Infof("ui agent component task info: %s", protobuf.MarshalJSONIgnoreError(info))

	if err = uiAgentComponent.NewExecuteUIAgentComponentTaskLogic(ctx, p.svcCtx, info).Execute(); err != nil {
		logger.Errorf("failed to execute the ui agent component task, payload: %s, error: %+v", task.Payload, err)
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}

func (p *UIAgentComponentTaskProcessor) setTaskInfoToCache(
	ctx context.Context, task *base.Task, taskID, executeID string,
) {
	var (
		info *taskInfo

		logger = logx.WithContext(ctx)
		now    = time.Now()
		key    = fmt.Sprintf("%s:%s:%s", common.ConstCacheKeyPrefixOfTaskInfo, taskID, executeID)
	)
	defer func() {
		if info != nil {
			var ctxDeadline time.Duration
			if deadline, ok := ctx.Deadline(); ok {
				ctxDeadline = time.Until(deadline)
			}

			value := protobuf.MarshalJSONIgnoreError(info)
			expiration := max(ctxDeadline, task.Timeout, time.Hour)
			result, err := p.svcCtx.RedisNode.Set(ctx, key, value, expiration).Result()
			if err != nil && !errors.Is(err, redis.Nil) {
				logger.Errorf(
					"failed to set the task info to cache, key: %s, value: %s, result: %s, error: %+v",
					key, value, result, err,
				)
			} else {
				logger.Infof("set the task info to cache, key: %s, value: %s, result: %s", key, value, result)
			}
		}
	}()

	result, err := p.svcCtx.RedisNode.Get(ctx, key).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		logger.Errorf("failed to get the task info from cache, key: %s, error: %+v", key, err)
	} else if result != "" {
		logger.Infof("get the task info from cache, key: %s, result: %s", key, result)

		info = &taskInfo{}
		if err = jsonx.UnmarshalFromString(result, info); err != nil {
			logger.Errorf(
				"failed to unmarshal the task info from cache, key: %s, result: %s, error: %+v", key, result, err,
			)
		} else {
			info.LastProcessedAt = now
			info.ProcessedTimes += 1
		}
	}
	if err != nil || result == "" {
		info = &taskInfo{
			Name:             task.Typename,
			Payload:          task.Payload,
			Queue:            task.Queue,
			FirstProcessedAt: now,
			LastProcessedAt:  now,
			ProcessedTimes:   1,
		}
	}
}

func convertToTaskInfo(req *dispatcherpb.WorkerReq) (*commonpb.UIAgentComponentTaskInfo, error) {
	if req == nil {
		return nil, errors.New("the worker request is null")
	}

	componentData := req.GetNodeData().GetUiAgentComponent()
	if componentData == nil {
		return nil, errors.Errorf("the execution data is null, execute_type: %s", req.GetExecuteType())
	}

	componentWorkerInfo := req.GetUiAgentComponent()
	if componentWorkerInfo == nil {
		return nil, errors.Errorf("the ui agent component worker info is null, execute_type: %s", req.GetExecuteType())
	}

	var times int32 = 1
	if componentWorkerInfo.GetTimes() > 1 {
		times = componentWorkerInfo.GetTimes()
	}

	info := &commonpb.UIAgentComponentTaskInfo{
		TaskId:          req.GetTaskId(),
		ExecuteId:       req.GetExecuteId(),
		ParentExecuteId: componentWorkerInfo.GetParentExecuteId(), // 父执行ID有可能为空
		ProjectId:       req.GetProjectId(),
		TriggerMode:     req.GetTriggerMode(),
		TriggerRule:     req.GetTriggerRule(),

		ComponentId:       componentData.GetComponentId(),
		ComponentName:     componentData.GetName(),
		ApplicationConfig: componentData.GetApplicationConfig(),
		Mode:              componentData.GetMode(),
		Steps:             componentData.GetSteps(),
		Expectation:       componentData.GetExpectation(),
		Variables:         componentData.GetVariables(),
		InputParameters:   componentData.GetInputParameters(),
		OutputParameters:  componentData.GetOutputParameters(),

		ExecuteType: componentWorkerInfo.GetExecuteType(),
		Device:      componentWorkerInfo.GetDevice(),
		Reinstall:   componentWorkerInfo.GetReinstall(),
		Restart:     componentWorkerInfo.GetRestart(),
		ReferenceId: componentWorkerInfo.GetReferenceId(),
		Times:       times, // 注：正常情况下，`uaworker`不需要关心此字段

		ExecutedBy: req.GetUserId(),
	}
	if err := info.ValidateAll(); err != nil {
		return nil, errors.Errorf(
			"failed to validate the ui agent component task info, info: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(info), err,
		)
	}

	return info, nil
}
