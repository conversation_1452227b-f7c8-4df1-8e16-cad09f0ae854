package server

import (
	"context"
	"os"
	"sync"
	"syscall"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/mqc/internal"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/mqc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/mqc/internal/svc"
)

type Server struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	process  *os.Process
	stopOnce sync.Once
}

// NewConsumeServer for single server startup
func NewConsumeServer(configFile string) (*server.Server, error) {
	return server.NewServer(Options(configFile)...)
}

// NewServer for combine server startup
func NewServer(c server.Config, w *log.ZapWriter) (service.Service, error) {
	p, err := os.FindProcess(os.Getpid())
	if err != nil {
		return nil, errors.Errorf("failed to find self process, error: %+v", err)
	}

	cc, ok := c.(config.Config)
	if !ok {
		return nil, errors.Errorf("failed to new mqc server, cause by the config[%T] isn't a mqc config", c)
	}

	if err = cc.ServiceConf.SetUp(); err != nil {
		return nil, errors.Errorf("failed to setup service config, error: %+v", err)
	}

	// 需要在 `ServiceConf.SetUp` 后再设置 `Writer`
	log.SetWriter(w)

	ctx := context.Background()
	s := &Server{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svc.NewServiceContext(cc),

		process: p,
	}

	if err = internal.HandleSetupOperations(s.svcCtx); err != nil {
		return nil, errors.Errorf("failed to handle setup operations, error: %+v", err)
	}

	return s, nil
}

// NewConfig new a config of server
func NewConfig(configFile string) server.Config {
	var c config.Config
	conf.MustLoad(configFile, &c, conf.UseEnv())

	return c
}

// Options as a param of `server.NewServer`
func Options(configFile string) []server.Option {
	return []server.Option{
		server.WithNewConfigFunc(
			func() server.Config {
				return NewConfig(configFile)
			},
		),
		server.WithNewServiceFunc(NewServer),
	}
}

func (s *Server) Start() {
	s.Infof("Starting %q at %s", s.svcCtx.Config.Name, time.Now().Format(time.DateTime))

	s.svcCtx.UIAgentWorkerConsumer.Start()
}

func (s *Server) Stop() {
	s.stopOnce.Do(
		func() {
			s.Infof("Stopping %q at %s", s.svcCtx.Config.Name, time.Now().Format(time.DateTime))

			// send an exit signal to other goroutines
			close(s.svcCtx.ExitChannel)
			s.svcCtx.ExitFlag = true

			// shutdown the consumer
			if err := s.process.Signal(syscall.SIGTERM); err != nil {
				s.Errorf("failed to send signal TERM to the consumer, pid: %d, error: %+v", s.process.Pid, err)
			}
			s.svcCtx.UIAgentWorkerConsumer.Stop()
		},
	)
}
