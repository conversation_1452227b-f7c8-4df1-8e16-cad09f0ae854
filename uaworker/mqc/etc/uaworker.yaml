Name: uaworker

Log:
  ServiceName: mqc.uaworker
  Encoding: plain
  Level: info
  Path: /app/logs/uaworker

#Prometheus:
#  Host: 0.0.0.0
#  Port: 9101
#  Path: /metrics
#
#Telemetry:
#  Name: mqc.staworker
#  Endpoint: http://tt-yw-tracing-jaeger.ttyuyin.com:9511
#  Sampler: 1.0
#  Batcher: zipkin
#
#DevServer:
#  Enabled: true
#  Port: 6470

Redis:
  Key: mqc.uaworker
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 24

DispatcherRedis:
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 4

Discovery:
  Target: 127.0.0.1:21511
  NonBlock: true
  Timeout: 0

Reporter:
  Endpoints:
    - 127.0.0.1:20511
  NonBlock: true
  Timeout: 0

UIAgentWorkerConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:uaworker
  ConsumerTag: mqc:uaworker
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 20
  MaxWorker: 0
  ShutdownTimeout: 5s

DispatcherProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:dispatcher
  Db: 20

ManagerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:manager
  Db: 20

ReporterProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:reporter
  Db: 20

ADB:
  PathOfADB: ../assets/adb/adb-darwin/adb
  Host: localhost
  Port: 5037

ClickPilot:
  BaseURL: 'http://127.0.0.1:8000'

LocalPath: ${LOCAL_PATH}
