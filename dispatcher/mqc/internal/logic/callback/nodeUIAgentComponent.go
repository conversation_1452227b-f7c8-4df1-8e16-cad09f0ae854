package callback

import (
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/dtctl"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

var _ Node = (*UIAgentComponentNode)(nil)

type UIAgentComponentNode struct {
	*BaseNode

	timesInfo *pb.UIAgentComponentTimesInfo
}

func NewUIAgentComponentNode(task *Callback) *UIAgentComponentNode {
	info, err := task.GetUIAgentComponentTimesInfo(task.Source().GetTaskId())
	if re, ok := errorx.RootError(err); ok && re.Code() != errorx.NotExists {
		task.Errorf("NewUIAgentComponentNode: %s", err)
	}

	return &UIAgentComponentNode{
		BaseNode: NewBaseNode(task),

		timesInfo: info,
	}
}

func (node *UIAgentComponentNode) Type() managerpb.ApiExecutionDataType {
	return managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT
}

func (node *UIAgentComponentNode) GetDtControl() *dtctl.DispatchTaskControl {
	if node.ctl != nil {
		return node.ctl
	}

	var member *dtctl.DispatchTaskMember

	data := node.Task().Source().GetUiAgentComponent()
	if data.GetParentExecuteId() != "" {
		// 父执行ID非空，即为多次执行，当前执行记录为子执行记录
		member = &dtctl.DispatchTaskMember{
			ComponentId:        data.GetComponentId(),
			ComponentType:      managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT,
			ComponentExecuteId: data.GetParentExecuteId(),
		}
	} else {
		// 父执行ID为空，即为单次执行，当前执行记录为主执行记录
		member = &dtctl.DispatchTaskMember{
			ComponentId:        data.GetComponentId(),
			ComponentType:      managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT,
			ComponentExecuteId: data.GetComponentExecuteId(),
		}
	}

	node.ctl = dtctl.NewDispatchTaskControl(node.Task().Context(), node.Task().ServiceContext().Redis, member)
	return node.ctl
}

func (node *UIAgentComponentNode) GetMember() *dtctl.DispatchTaskMember {
	data := node.Task().Source().GetUiAgentComponent()
	return &dtctl.DispatchTaskMember{
		ComponentId:        data.GetComponentId(),
		ComponentType:      managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT,
		ComponentExecuteId: data.GetComponentExecuteId(),
	}
}

func (node *UIAgentComponentNode) State() pb.ComponentState {
	return node.Task().Source().GetUiAgentComponent().GetComponentState()
}

func (node *UIAgentComponentNode) SetState(s pb.ComponentState) {
	node.Task().Source().GetUiAgentComponent().ComponentState = s
}

func (node *UIAgentComponentNode) TaskInfoProcessorSync() {
	switch node.State() {
	case pb.ComponentState_Panic, pb.ComponentState_Stop, pb.ComponentState_Invalid:
		return
	}

	if node.timesInfo == nil {
		return
	}

	defer func() {
		_ = node.Task().UpdateUIAgentComponentTimesInfo(node.Task().Source().GetTaskId(), node.timesInfo)
	}()
	if err := node.publish(); err != nil {
		node.Task().Errorf("UIAgentComponentNode: publish: %s", err)
	}
}

func (node *UIAgentComponentNode) Record() error {
	var (
		source          = node.Task().Source()
		component       = source.GetUiAgentComponent()
		parentExecuteID = component.GetParentExecuteId()
	)

	if parentExecuteID != "" {
		var (
			successes, passes int32

			state = node.State()
		)

		if node.timesInfo != nil {
			successes = node.timesInfo.GetSuccess()
			passes = node.timesInfo.GetPasses()

			switch state {
			case pb.ComponentState_Success,
				pb.ComponentState_Warning,
				pb.ComponentState_Skip,
				pb.ComponentState_Failure:
				if node.timesInfo.GetExceptions() > 0 {
					state = pb.ComponentState_Error
				} else if node.timesInfo.GetTotal() != successes {
					state = pb.ComponentState_Failure
				}
			case pb.ComponentState_Stop:
				if node.timesInfo.GetExceptions() > 0 {
					state = pb.ComponentState_Error
				}
			}
		}

		_, err := node.Task().ServiceContext().UIAgentReporterRPC.ModifyUIAgentComponentRecord(
			node.Task().Context(), &reporterpb.ModifyUIAgentComponentRecordReq{
				TaskId:        source.GetTaskId(),
				ExecuteId:     parentExecuteID,
				ProjectId:     source.GetProjectId(),
				ComponentId:   component.GetComponentId(),
				ExecutedSteps: 0, // 执行多次的父记录，执行步骤数设置为0
				Successes:     successes,
				Passes:        passes,
				Status:        state.String(),
				EndedAt:       timestamppb.New(time.Now()),
			},
		)
		return err
	}

	return nil
}

func (node *UIAgentComponentNode) RecordCallback() (err error) {
	var (
		source          = node.Task().Source()
		component       = source.GetUiAgentComponent()
		taskID          = source.GetTaskId()
		executeID       = component.GetComponentExecuteId()
		parentExecuteID = component.GetParentExecuteId()
		projectID       = source.GetProjectId()
		componentID     = component.GetComponentId()
	)

	var successes, passes, exceptions int32
	switch v := component.GetComponentState(); v {
	case pb.ComponentState_Success:
		successes = 1
		passes = 1
	case pb.ComponentState_Warning, pb.ComponentState_Skip, pb.ComponentState_Failure:
		passes = 1
	case pb.ComponentState_Panic, pb.ComponentState_Error:
		exceptions = 1
	}

	if _, err = node.Task().ServiceContext().UIAgentReporterRPC.ModifyUIAgentComponentRecord(
		node.Task().Context(), &reporterpb.ModifyUIAgentComponentRecordReq{
			TaskId:        taskID,
			ExecuteId:     executeID,
			ProjectId:     projectID,
			ComponentId:   componentID,
			ExecutedSteps: component.GetExecutedSteps(),
			Successes:     successes,
			Passes:        passes,
			Status:        component.GetComponentState().String(),
			EndedAt:       timestamppb.New(time.Now()),
			ErrMsg:        component.GetErrMsg(),
		},
	); err != nil {
		return err
	}

	if node.timesInfo != nil {
		node.timesInfo.Success += successes
		node.timesInfo.Passes += passes
		node.timesInfo.Exceptions += exceptions

		if parentExecuteID != "" {
			if _, err = node.Task().ServiceContext().UIAgentReporterRPC.ModifyUIAgentComponentRecord(
				node.Task().Context(), &reporterpb.ModifyUIAgentComponentRecordReq{
					TaskId:        taskID,
					ExecuteId:     parentExecuteID,
					ProjectId:     projectID,
					ComponentId:   componentID,
					ExecutedSteps: 0, // 执行多次的父记录，执行步骤数设置为0
					Successes:     node.timesInfo.GetSuccess(),
					Passes:        node.timesInfo.GetPasses(),
				},
			); err != nil {
				node.Task().Errorf(
					"failed to modify the ui agent component parent record, task_id: %s, execute_id: %s, parent_execute_id: %s, error: %+v",
					taskID, executeID, parentExecuteID, err,
				)
			}
		}
	}

	return nil
}

func (node *UIAgentComponentNode) Teardown() error {
	return nil
}

func (node *UIAgentComponentNode) publish() error {
	if node.timesInfo == nil || node.timesInfo.GetSent() >= node.timesInfo.GetTotal() {
		return nil
	}

	req := node.timesInfo.GetReq()
	req.ExecuteId = utils.GenExecuteId()
	if v, ok := req.GetData().(*pb.WorkerReq_UiAgentComponent); ok && v.UiAgentComponent != nil {
		v.UiAgentComponent.ComponentExecuteId = req.ExecuteId
	}

	if err := node.createRecord(req); err != nil {
		return err
	}

	if _, err := node.Task().ServiceContext().UIAgentWorkerProducer.Send(
		node.Task().Context(), base.NewTask(
			constants.MQTaskTypeUIAgentWorkerExecuteComponentTask,
			protobuf.MarshalJSONIgnoreError(req),
			base.WithMaxRetryOptions(0),
			base.WithTimeoutOptions(time.Hour),
			base.WithRetentionOptions(time.Hour),
		), mq.ConvertPbEnumerationToQueuePriority(req.GetPriorityType()),
	); err != nil {
		return errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	node.timesInfo.Sent++
	return nil
}

func (node *UIAgentComponentNode) createRecord(source *pb.WorkerReq) error {
	data := source.GetNodeData().GetUiAgentComponent()
	component := source.GetUiAgentComponent()

	var times int32
	if component.GetParentExecuteId() != "" {
		times = 1 // 子记录的执行次数都为1
	} else {
		times = component.GetTimes()
	}

	req := &reporterpb.CreateUIAgentComponentRecordReq{
		TaskId:            source.GetTaskId(),
		ExecuteId:         source.GetExecuteId(),
		ParentExecuteId:   component.GetParentExecuteId(),
		ProjectId:         source.GetProjectId(),
		ComponentId:       component.GetComponentId(),
		ComponentName:     data.GetName(),
		TriggerMode:       source.GetTriggerMode(),
		ExecuteType:       component.GetExecuteType(),
		ApplicationConfig: data.GetApplicationConfig(),
		Mode:              data.GetMode(),
		Steps:             data.GetSteps(),
		Expectation:       data.GetExpectation(),
		Variables:         data.GetVariables(),
		InputParameters:   data.GetInputParameters(),
		OutputParameters:  data.GetOutputParameters(),
		Device:            component.GetDevice(),
		Reinstall:         component.GetReinstall(),
		Restart:           component.GetRestart(),
		ReferenceId:       component.GetReferenceId(),
		Times:             times,
		Status:            pb.ComponentState_Pending.String(),
		ExecutedBy:        source.GetUserId(),
		StartedAt:         timestamppb.New(time.Now()),
	}
	return node.Task().CreateUIAgentComponentRecord(req)
}
